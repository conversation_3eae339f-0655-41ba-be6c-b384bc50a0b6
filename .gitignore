# ===================================================================
# LaTeX 编译产生的文件 (LaTeX compilation files)
# ===================================================================

# 辅助文件 (Auxiliary files)
*.aux
*.lof
*.log
*.lot
*.fls
*.out
*.toc
*.fmt
*.fot
*.cb
*.cb2
*.lb

# 中间文件 (Intermediate files)
*.dvi
*.xdv
*.bbl
*.bcf
*.blg
*.fdb_latexmk
*.synctex
*.synctex(busy)
*.synctex.gz
*.synctex.gz(busy)
*.pdfsync

# Beamer 相关文件 (Beamer specific files)
*.nav
*.snm
*.vrb

# 索引文件 (Index files)
*.idx
*.ilg
*.ind
*.ist

# 术语表文件 (Glossary files)
*.acn
*.acr
*.glg
*.glo
*.gls
*.glsdefs

# 参考文献文件 (Bibliography files)
*.brf
*.run.xml

# 字体文件 (Font files)
*.tfm

# ===================================================================
# 编辑器和 IDE 文件 (Editor and IDE files)
# ===================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# TeXstudio
*.txss

# TeXShop
*.fdb_latexmk
*.synctex.gz

# Kile
*.backup

# ===================================================================
# 操作系统文件 (Operating system files)
# ===================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================================================
# 构建目录 (Build directories)
# ===================================================================

# 保留主要的 PDF 输出文件，但忽略其他构建产物
# (Keep main PDF output files, but ignore other build artifacts)
build/*.aux
build/*.fdb_latexmk
build/*.fls
build/*.log
build/*.nav
build/*.out
build/*.snm
build/*.synctex.gz
build/*.toc
build/*.xdv

# sections 目录下的构建文件
sections/build/

# ===================================================================
# 临时文件 (Temporary files)
# ===================================================================

# 临时 LaTeX 文件
*-temp.tex
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# ===================================================================
# 其他 (Miscellaneous)
# ===================================================================

# 压缩文件（如果是临时的）
*.zip
*.tar.gz
*.rar

# 但保留可能需要的图片和资源文件
# (Keep potentially needed image and resource files)
# 注释掉以下行，因为我们需要保留图片
# *.png
# *.jpg
# *.jpeg
# *.pdf
